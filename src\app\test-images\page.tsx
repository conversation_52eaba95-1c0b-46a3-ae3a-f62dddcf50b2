'use client';

import { getAllImages } from '@/data/imageGallery';

export default function TestImages() {
  const images = getAllImages();

  return (
    <div className="container mx-auto py-16 px-4">
      <h1 className="text-3xl font-bold mb-8">Image Test Page</h1>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {images.map((image) => (
          <div key={image.id} className="border rounded-lg overflow-hidden">
            <img
              src={image.src}
              alt={image.title}
              className="w-full h-32 object-cover"
              onError={(e) => {
                console.error(`Failed to load image: ${image.src}`);
                e.currentTarget.style.backgroundColor = '#f3f4f6';
                e.currentTarget.style.display = 'flex';
                e.currentTarget.style.alignItems = 'center';
                e.currentTarget.style.justifyContent = 'center';
                e.currentTarget.innerHTML = 'Image not found';
              }}
            />
            <div className="p-2">
              <h3 className="font-semibold text-sm">{image.title}</h3>
              <p className="text-xs text-gray-600">{image.src}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
