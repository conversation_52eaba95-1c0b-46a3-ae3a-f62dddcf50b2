'use client';

import * as React from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '@/lib/utils';

interface CardProps extends Omit<React.HTMLAttributes<HTMLDivElement>,
  | 'onDrag' | 'onDragEnd' | 'onDragEnter' | 'onDragExit' | 'onDragLeave' | 'onDragOver' | 'onDragStart' | 'onDrop'
  | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'
  | 'onTransitionEnd' | 'onTransitionStart'
  | 'transition'
> {
  animation?: 'none' | 'hover-lift' | 'tilt' | 'glow' | 'magnetic' | 'border-beam';
  glowColor?: string;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, animation = 'hover-lift', glowColor = 'rgba(59, 130, 246, 0.5)', ...props }, ref) => {
    const [isHovered, setIsHovered] = React.useState(false);
    const cardRef = React.useRef<HTMLDivElement>(null);

    const x = useMotionValue(0);
    const y = useMotionValue(0);

    const mouseXSpring = useSpring(x);
    const mouseYSpring = useSpring(y);

    const rotateX = useTransform(mouseYSpring, [-0.5, 0.5], ["17.5deg", "-17.5deg"]);
    const rotateY = useTransform(mouseXSpring, [-0.5, 0.5], ["-17.5deg", "17.5deg"]);

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!cardRef.current || animation === 'none') return;

      const rect = cardRef.current.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      const xPct = mouseX / width - 0.5;
      const yPct = mouseY / height - 0.5;

      x.set(xPct);
      y.set(yPct);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
      x.set(0);
      y.set(0);
    };

    const getAnimationProps = () => {
      if (animation === 'none') return {};

      switch (animation) {
        case 'hover-lift':
          return {
            whileHover: { y: -8, scale: 1.02 },
            transition: { type: "spring" as const, stiffness: 300, damping: 20 }
          };

        case 'tilt':
          return {
            style: { rotateX, rotateY, transformStyle: "preserve-3d" as const },
            whileHover: { scale: 1.05 },
            transition: { type: "spring" as const, stiffness: 300, damping: 20 }
          };

        case 'glow':
          return {
            whileHover: {
              scale: 1.02,
              boxShadow: `0 0 20px ${glowColor}, 0 0 40px ${glowColor}`,
            },
            transition: { duration: 0.3 }
          };

        case 'magnetic':
          return {
            x: mouseXSpring,
            y: mouseYSpring,
            whileHover: { scale: 1.05 },
            transition: { type: "spring" as const, stiffness: 300, damping: 20 }
          };

        case 'border-beam':
          return {
            whileHover: { scale: 1.02 },
            transition: { duration: 0.3 }
          };

        default:
          return {};
      }
    };

    if (animation === 'none') {
      return (
        <div
          ref={ref}
          className={cn(
            'rounded-xl border bg-card text-card-foreground shadow',
            className
          )}
          {...props}
        />
      );
    }

    return (
      <motion.div
        ref={cardRef}
        className={cn(
          "relative rounded-xl border bg-card text-card-foreground shadow-lg overflow-hidden",
          animation === 'border-beam' && "border-transparent",
          className
        )}
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={handleMouseLeave}
        {...props}
        {...getAnimationProps()}
      >
        {/* Border beam effect */}
        {animation === 'border-beam' && (
          <motion.div
            className="absolute inset-0 rounded-xl"
            style={{
              background: 'linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent)',
              backgroundSize: '200% 100%',
              padding: '1px',
            }}
            animate={isHovered ? {
              backgroundPosition: ['200% 0%', '-200% 0%'],
            } : {}}
            transition={{
              duration: 2,
              repeat: isHovered ? Infinity : 0,
              ease: "linear",
            }}
          >
            <div className="w-full h-full bg-card rounded-xl" />
          </motion.div>
        )}

        {/* Shine effect */}
        <motion.div
          className="absolute inset-0 opacity-0 pointer-events-none"
          style={{
            background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
            backgroundSize: '200% 200%',
          }}
          animate={isHovered ? {
            opacity: [0, 1, 0],
            backgroundPosition: ['-200% -200%', '200% 200%'],
          } : {}}
          transition={{ duration: 0.6 }}
        />

        {/* Content */}
        <div className="relative z-10">
          {props.children}
        </div>
      </motion.div>
    );
  }
);
Card.displayName = 'Card';

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6', className)}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('font-semibold leading-none tracking-tight', className)}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
));
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
