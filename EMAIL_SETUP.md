# Email Setup for Contact Form

The contact form is configured to send emails to `<EMAIL>` when users submit the form.

## Setup Instructions

### 1. Configure Environment Variables

Copy `.env.example` to `.env.local` and fill in your email credentials:

```bash
cp .env.example .env.local
```

### 2. Gmail Setup (Recommended)

If using Gmail as the sending email service:

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
3. **Update `.env.local`**:
   ```
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-character-app-password
   ```

### 3. Alternative Email Services

You can also use other email services by modifying the transporter configuration in `src/app/api/contact/route.ts`:

#### SendGrid
```javascript
const transporter = nodemailer.createTransporter({
  service: 'SendGrid',
  auth: {
    user: 'apikey',
    pass: process.env.SENDGRID_API_KEY,
  },
});
```

#### Outlook/Hotmail
```javascript
const transporter = nodemailer.createTransporter({
  service: 'hotmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});
```

#### Custom SMTP
```javascript
const transporter = nodemailer.createTransporter({
  host: 'your-smtp-server.com',
  port: 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});
```

## How It Works

1. User fills out the contact form on `/contact`
2. Form data is sent to `/api/contact` endpoint
3. API validates the data and sends an email to `<EMAIL>`
4. Email includes all form details in a formatted HTML template
5. User receives confirmation that the message was sent

## Email Template

The email includes:
- Contact information (name, email, phone, project type)
- User's message
- Professional formatting with company branding
- Reply-to address set to the user's email

## Testing

To test the contact form:
1. Make sure environment variables are set up
2. Run the development server: `npm run dev`
3. Navigate to `/contact`
4. Fill out and submit the form
5. Check the destination email inbox

## Troubleshooting

- **"Failed to send email"**: Check your email credentials and internet connection
- **Gmail authentication errors**: Ensure 2FA is enabled and you're using an app password
- **Email not received**: Check spam folder and verify the destination email address
