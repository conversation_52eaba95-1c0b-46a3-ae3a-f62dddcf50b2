import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    console.log('Contact API called - Environment:', process.env.NODE_ENV);
    const { name, email, phone, project, message } = await request.json();

    // Validate required fields
    if (!name || !phone || !message) {
      return NextResponse.json(
        { error: 'Name, phone, and message are required' },
        { status: 400 }
      );
    }

    // Check if email environment variables are configured
    console.log('Environment check - EMAIL_USER:', process.env.EMAIL_USER ? 'Set' : 'Not set');
    console.log('Environment check - EMAIL_PASS:', process.env.EMAIL_PASS ? 'Set' : 'Not set');

    const isEmailConfigured = process.env.EMAIL_USER &&
                              process.env.EMAIL_PASS &&
                              process.env.EMAIL_USER.includes('@') &&
                              process.env.EMAIL_PASS.length > 10;

    // Force email sending if we have any credentials (for debugging)
    const forceEmailSending = process.env.EMAIL_USER === '<EMAIL>';

    if (!isEmailConfigured && !forceEmailSending) {
      console.log('Email not configured properly. Logging form submission instead.');
      console.log('EMAIL_USER exists:', !!process.env.EMAIL_USER);
      console.log('EMAIL_PASS exists:', !!process.env.EMAIL_PASS);

      // Log the form submission for development/testing
      console.log('=== CONTACT FORM SUBMISSION ===');
      console.log('Name:', name);
      console.log('Email:', email || 'Not provided');
      console.log('Phone:', phone);
      console.log('Project:', project || 'Not specified');
      console.log('Message:', message);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Destination: <EMAIL>');
      console.log('================================');

      // Return success response (for development)
      return NextResponse.json(
        { message: 'Form submitted successfully (logged to console - email not configured)' },
        { status: 200 }
      );
    }

    console.log('Proceeding with email sending...');

    // Create transporter with Vercel-optimized settings
    const transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 465,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false
      },
      connectionTimeout: 60000,
      greetingTimeout: 30000,
      socketTimeout: 60000,
    });

    // Verify transporter configuration
    try {
      await transporter.verify();
      console.log('Email transporter verified successfully');
    } catch (verifyError) {
      console.error('Email transporter verification failed:', verifyError);

      // Log the form submission as fallback
      console.log('=== CONTACT FORM SUBMISSION (EMAIL FAILED) ===');
      console.log('Name:', name);
      console.log('Email:', email || 'Not provided');
      console.log('Phone:', phone);
      console.log('Project:', project || 'Not specified');
      console.log('Message:', message);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Destination: <EMAIL>');
      console.log('Error:', verifyError);
      console.log('===============================================');

      // Return success but indicate email issue
      return NextResponse.json(
        { message: 'Form submitted successfully (email service temporarily unavailable)' },
        { status: 200 }
      );
    }

    // Email content
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: `New Contact Form Submission from ${name}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px;">
            New Contact Form Submission
          </h2>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin-top: 0;">Contact Information</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
            ${project ? `<p><strong>Project Type:</strong> ${project}</p>` : ''}
          </div>

          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin-top: 0;">Message</h3>
            <p style="white-space: pre-wrap;">${message}</p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; color: #64748b; font-size: 14px;">
            <p>This message was sent from the Kani Timber Sales Center contact form.</p>
            <p>Please respond to the customer at: ${email}</p>
          </div>
        </div>
      `,
    };

    // Send email
    console.log('Attempting to send email to:', '<EMAIL>');
    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);

    return NextResponse.json(
      { message: 'Email sent successfully', messageId: result.messageId },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in contact API:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to send email';
    if (error instanceof Error) {
      if (error.message.includes('authentication')) {
        errorMessage = 'Email authentication failed. Please check email credentials.';
      } else if (error.message.includes('configuration')) {
        errorMessage = 'Email service configuration error.';
      } else if (error.message.includes('network') || error.message.includes('ENOTFOUND')) {
        errorMessage = 'Network error. Please check your internet connection.';
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
