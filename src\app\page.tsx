'use client';
import HeroSection from '@/components/HeroSection';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/Card';
import BackgroundEffects from '@/components/BackgroundEffects';
import TextReveal from '@/components/TextReveal';
import FloatingElements from '@/components/FloatingElements';
import { AnimatedButton } from '@/components/AnimatedButton';
import ImageGallery from '@/components/ImageGallery';
import { motion } from 'framer-motion';
import { staggerContainer, staggerItem } from '@/lib/utils';

const features = [
  {
    title: 'Premium Quality Timber',
    description: 'We source only the finest timber from sustainably managed forests, ensuring both quality and environmental responsibility.',
    icon: '🌲',
    color: 'emerald'
  },
  {
    title: 'Comprehensive Range',
    description: 'From exotic hardwoods to engineered lumber, our extensive inventory meets every construction and design need.',
    icon: '📐',
    color: 'blue'
  },
  {
    title: 'Expert Consultation',
    description: 'Our experienced team provides professional guidance to help you select the perfect materials for your project.',
    icon: '👨‍🔧',
    color: 'purple'
  }
];

const stats = [
  { number: '25+', label: 'Years Experience' },
  { number: '10K+', label: 'Happy Customers' },
  { number: '50+', label: 'Wood Species' },
  { number: '99%', label: 'Satisfaction Rate' }
];

export default function Home() {
  return (
    <div>
      <HeroSection />

      {/* Features Section */}
      <BackgroundEffects variant="dots" intensity="low">
        <section className="container mx-auto py-20 px-4">
          <div className="text-center mb-16">
            <TextReveal
              variant="wave"
              className="text-4xl font-bold mb-6"
            >
              Why Choose Kani Timber?
            </TextReveal>
            <TextReveal
              variant="fade-up"
              delay={0.3}
              className="text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              With decades of experience and a commitment to excellence, we deliver premium timber solutions that exceed expectations.
            </TextReveal>
          </div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {features.map((feature, index) => (
              <motion.div key={index} variants={staggerItem}>
                <Card
                  animation="tilt"
                  className="h-full text-center group hover:shadow-2xl transition-all duration-500"
                >
                  <CardHeader className="pb-4">
                    <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-2xl font-bold group-hover:text-blue-600 transition-colors">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </section>
      </BackgroundEffects>

      {/* Stats Section */}
      {/* <BackgroundEffects variant="gradient" className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Trusted by Thousands
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Our commitment to quality and service has made us the preferred choice for contractors, builders, and DIY enthusiasts.
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={staggerItem}
                className="text-center"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 * index, type: "spring", stiffness: 200 }}
                  className="text-5xl font-bold text-white mb-2"
                >
                  {stat.number}
                </motion.div>
                <div className="text-white/80 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </BackgroundEffects> */}

      {/* CTA Section */}
      <BackgroundEffects variant="waves" intensity="medium">
        <FloatingElements count={10} />
        <section className="container mx-auto py-20 px-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="text-center bg-white/10 backdrop-blur-sm rounded-3xl p-12 border border-white/20"
          >
            <h2 className="text-4xl font-bold mb-6">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Get in touch with our experts today and discover how we can help bring your vision to life with premium timber solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <AnimatedButton
                animation="ripple"
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <a href="/products">Browse Products</a>
              </AnimatedButton>
            </div>
          </motion.div>
        </section>
      </BackgroundEffects>
    </div>
  );
}