'use client';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';
import { usePathname } from "next/navigation";
// Update the import path below if your Button component is located elsewhere
// import { Button } from './ui/button';
import { Button } from '@/components/Button'; // Update this path to the correct location of your Button component

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-primary text-white p-4 sticky top-0 z-50 shadow-lg"
    >
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold hover:text-blue-400">
          <PERSON><PERSON>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-4">
          <Link href="/">
            <Button
              variant="ghost"
              className={`text-white cursor-pointer ${pathname === "/" ? "bg-white/20 font-bold" : ""}`}
            >
              Home
            </Button>
          </Link>
          <Link href="/about">
            <Button
              variant="ghost"
              className={`text-white cursor-pointer ${pathname === "/about" ? "bg-white/20 font-bold" : ""}`}
            >
              About
            </Button>
          </Link>
          <Link href="/products">
            <Button
              variant="ghost"
              className={`text-white cursor-pointer ${pathname === "/products" ? "bg-white/20 font-bold" : ""}`}
            >
              Products
            </Button>
          </Link>
          <Link href="/contact">
            <Button
              variant="ghost"
              className={`text-white cursor-pointer ${pathname === "/contact" ? "bg-white/20 font-bold" : ""}`}
            >
              Contact
            </Button>
          </Link>
        </nav>

        {/* Mobile Menu Button */}
        <button
          onClick={toggleMenu}
          className="md:hidden flex flex-col justify-center items-center w-8 h-8 space-y-1 cursor-pointer"
          aria-label="Toggle menu"
        >
          <motion.span
            animate={isMenuOpen ? { rotate: 45, y: 6 } : { rotate: 0, y: 0 }}
            className="w-6 h-0.5 bg-white block transition-all"
          />
          <motion.span
            animate={isMenuOpen ? { opacity: 0 } : { opacity: 1 }}
            className="w-6 h-0.5 bg-white block transition-all"
          />
          <motion.span
            animate={isMenuOpen ? { rotate: -45, y: -6 } : { rotate: 0, y: 0 }}
            className="w-6 h-0.5 bg-white block transition-all"
          />
        </button>
      </div>

      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.nav
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden mt-4 border-t border-white/20 pt-4"
          >
            <div className="flex flex-col space-y-2">
              <Link href="/" onClick={closeMenu}>
                <Button
                  variant="ghost"
                  className={`text-white cursor-pointer w-full text-left justify-start ${pathname === "/" ? "bg-white/20 font-bold" : ""}`}
                >
                  Home
                </Button>
              </Link>
              <Link href="/about" onClick={closeMenu}>
                <Button
                  variant="ghost"
                  className={`text-white cursor-pointer w-full text-left justify-start ${pathname === "/about" ? "bg-white/20 font-bold" : ""}`}
                >
                  About
                </Button>
              </Link>
              <Link href="/products" onClick={closeMenu}>
                <Button
                  variant="ghost"
                  className={`text-white cursor-pointer w-full text-left justify-start ${pathname === "/products" ? "bg-white/20 font-bold" : ""}`}
                >
                  Products
                </Button>
              </Link>
              <Link href="/contact" onClick={closeMenu}>
                <Button
                  variant="ghost"
                  className={`text-white cursor-pointer w-full text-left justify-start ${pathname === "/contact" ? "bg-white/20 font-bold" : ""}`}
                >
                  Contact
                </Button>
              </Link>
            </div>
          </motion.nav>
        )}
      </AnimatePresence>
    </motion.header>
  );
}