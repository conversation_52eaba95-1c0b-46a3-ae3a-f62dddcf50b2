'use client';

import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { useState, useRef } from 'react';
import { cn } from '@/lib/utils';

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'hover-lift' | 'tilt' | 'glow' | 'magnetic' | 'border-beam';
  glowColor?: string;
}

export default function AnimatedCard({ 
  children, 
  className = '',
  variant = 'hover-lift',
  glowColor = 'rgba(59, 130, 246, 0.5)'
}: AnimatedCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);
  
  const rotateX = useTransform(mouseYSpring, [-0.5, 0.5], ["17.5deg", "-17.5deg"]);
  const rotateY = useTransform(mouseXSpring, [-0.5, 0.5], ["-17.5deg", "17.5deg"]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;
    
    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const xPct = mouseX / width - 0.5;
    const yPct = mouseY / height - 0.5;
    
    x.set(xPct);
    y.set(yPct);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    x.set(0);
    y.set(0);
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'hover-lift':
        return {
          whileHover: { y: -8, scale: 1.02 },
          transition: { type: "spring" as const, stiffness: 300, damping: 20 }
        };
      
      case 'tilt':
        return {
          style: { rotateX, rotateY, transformStyle: "preserve-3d" as const },
          whileHover: { scale: 1.05 },
          transition: { type: "spring" as const, stiffness: 300, damping: 20 }
        };
      
      case 'glow':
        return {
          whileHover: { 
            scale: 1.02,
            boxShadow: `0 0 20px ${glowColor}, 0 0 40px ${glowColor}`,
          },
          transition: { duration: 0.3 }
        };
      
      case 'magnetic':
        return {
          x: mouseXSpring,
          y: mouseYSpring,
          whileHover: { scale: 1.05 },
          transition: { type: "spring" as const, stiffness: 300, damping: 20 }
        };
      
      case 'border-beam':
        return {
          whileHover: { scale: 1.02 },
          transition: { duration: 0.3 }
        };
      
      default:
        return {};
    }
  };

  // Border beam styles are applied via CSS classes instead

  return (
    <motion.div
      ref={ref}
      className={cn(
        "relative rounded-xl border bg-card text-card-foreground shadow-lg overflow-hidden",
        variant === 'border-beam' && "border-transparent",
        className
      )}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      {...getVariantStyles()}
    >
      {/* Border beam effect */}
      {variant === 'border-beam' && (
        <motion.div
          className="absolute inset-0 rounded-xl"
          style={{
            background: 'linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent)',
            backgroundSize: '200% 100%',
            padding: '1px',
          }}
          animate={isHovered ? {
            backgroundPosition: ['200% 0%', '-200% 0%'],
          } : {}}
          transition={{
            duration: 2,
            repeat: isHovered ? Infinity : 0,
            ease: "linear",
          }}
        >
          <div className="w-full h-full bg-card rounded-xl" />
        </motion.div>
      )}
      
      {/* Shine effect */}
      <motion.div
        className="absolute inset-0 opacity-0 pointer-events-none"
        style={{
          background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
          backgroundSize: '200% 200%',
        }}
        animate={isHovered ? {
          opacity: [0, 1, 0],
          backgroundPosition: ['-200% -200%', '200% 200%'],
        } : {}}
        transition={{ duration: 0.6 }}
      />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
}
