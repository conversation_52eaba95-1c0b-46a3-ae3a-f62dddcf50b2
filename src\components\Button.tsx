'use client';

import * as React from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';
import { cn } from '@/lib/utils';

export interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>,
    | 'onDrag' | 'onDragEnd' | 'onDragEnter' | 'onDragExit' | 'onDragLeave' | 'onDragOver' | 'onDragStart' | 'onDrop'
    | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'
    | 'onTransitionEnd' | 'onTransitionStart'
  > {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  animation?: 'shimmer' | 'ripple' | 'magnetic' | 'glow' | 'bounce' | 'none';
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'default',
    size = 'default',
    animation = 'none',
    asChild = false,
    children,
    ...props
  }, ref) => {
    const [isClicked, setIsClicked] = React.useState(false);
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([]);
    const buttonRef = React.useRef<HTMLButtonElement>(null);

    const x = useMotionValue(0);
    const y = useMotionValue(0);
    const mouseXSpring = useSpring(x);
    const mouseYSpring = useSpring(y);

    const Comp = asChild ? motion.span : motion.button;

    const baseClasses = 'relative inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 overflow-hidden';

    const variantClasses = {
      default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',
      destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
      outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
      secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'text-primary underline-offset-4 hover:underline',
    };

    const sizeClasses = {
      default: 'h-9 px-4 py-2',
      sm: 'h-8 rounded-md px-3 text-xs',
      lg: 'h-10 rounded-md px-8',
      icon: 'h-9 w-9',
    };

    const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (!buttonRef.current || animation !== 'magnetic') return;

      const rect = buttonRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      x.set((e.clientX - centerX) * 0.1);
      y.set((e.clientY - centerY) * 0.1);
    };

    const handleMouseLeave = () => {
      x.set(0);
      y.set(0);
    };

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (animation === 'ripple') {
        const rect = e.currentTarget.getBoundingClientRect();
        const rippleX = e.clientX - rect.left;
        const rippleY = e.clientY - rect.top;

        const newRipple = {
          id: Date.now(),
          x: rippleX,
          y: rippleY,
        };

        setRipples(prev => [...prev, newRipple]);

        setTimeout(() => {
          setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
        }, 600);
      }

      if (animation === 'bounce') {
        setIsClicked(true);
        setTimeout(() => setIsClicked(false), 150);
      }

      props.onClick?.(e);
    };

    const getAnimationProps = () => {
      if (animation === 'none') return {};

      switch (animation) {
        case 'magnetic':
          return {
            x: mouseXSpring,
            y: mouseYSpring,
            whileHover: { scale: 1.05 },
            whileTap: { scale: 0.95 },
          };

        case 'glow':
          return {
            whileHover: {
              boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)',
              scale: 1.05
            },
            whileTap: { scale: 0.95 },
          };

        case 'bounce':
          return {
            whileHover: { scale: 1.05 },
            whileTap: { scale: 0.9 },
            animate: isClicked ? { scale: [1, 1.1, 1] } : {},
          };

        default:
          return {
            whileHover: { scale: 1.02 },
            whileTap: { scale: 0.98 },
          };
      }
    };

    if (animation === 'none') {
      return (
        <button
          className={cn(
            baseClasses.replace('overflow-hidden', ''),
            variantClasses[variant],
            sizeClasses[size],
            className
          )}
          ref={ref}
          {...props}
        >
          {children}
        </button>
      );
    }

    return (
      <Comp
        ref={buttonRef}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        {...getAnimationProps()}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
        {...props}
      >
        {/* Shimmer effect */}
        {animation === 'shimmer' && (
          <motion.div
            className="absolute inset-0 opacity-0"
            style={{
              background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%)',
              backgroundSize: '200% 200%',
            }}
            whileHover={{
              opacity: [0, 1, 0],
              backgroundPosition: ['-200% -200%', '200% 200%'],
            }}
            transition={{ duration: 0.6 }}
          />
        )}

        {/* Ripple effects */}
        {animation === 'ripple' && ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            className="absolute rounded-full bg-white/30 pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          />
        ))}

        <span className="relative z-10">{children}</span>
      </Comp>
    );
  }
);

Button.displayName = 'Button';

export { Button };
