'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface TextRevealProps {
  children: string;
  className?: string;
  variant?: 'fade-up' | 'slide-in' | 'typewriter' | 'wave' | 'char-by-char';
  delay?: number;
  duration?: number;
}

export default function TextReveal({ 
  children, 
  className = '',
  variant = 'fade-up',
  delay = 0,
  duration = 0.8
}: TextRevealProps) {
  const words = children.split(' ');
  const chars = children.split('');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: variant === 'char-by-char' ? 0.03 : 0.1,
        delayChildren: delay,
      },
    },
  };

  const getItemVariants = () => {
    switch (variant) {
      case 'fade-up':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0, transition: { duration } },
        };
      
      case 'slide-in':
        return {
          hidden: { opacity: 0, x: -20 },
          visible: { opacity: 1, x: 0, transition: { duration } },
        };
      
      case 'typewriter':
        return {
          hidden: { opacity: 0, width: 0 },
          visible: { opacity: 1, width: 'auto', transition: { duration: duration * 2 } },
        };
      
      case 'wave':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { 
            opacity: 1, 
            y: [20, -5, 0], 
            transition: {
              duration,
              times: [0, 0.5, 1]
            }
          },
        };
      
      case 'char-by-char':
        return {
          hidden: { opacity: 0, y: 10 },
          visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
        };
      
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration } },
        };
    }
  };

  const itemVariants = getItemVariants();

  if (variant === 'typewriter') {
    return (
      <motion.div
        className={cn("overflow-hidden", className)}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.span
          variants={itemVariants}
          className="inline-block"
        >
          {children}
        </motion.span>
      </motion.div>
    );
  }

  if (variant === 'char-by-char') {
    return (
      <motion.div
        className={className}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {chars.map((char, index) => (
          <motion.span
            key={index}
            variants={itemVariants}
            className="inline-block"
            style={{ whiteSpace: char === ' ' ? 'pre' : 'normal' }}
          >
            {char}
          </motion.span>
        ))}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={itemVariants}
          className="inline-block mr-1"
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  );
}
