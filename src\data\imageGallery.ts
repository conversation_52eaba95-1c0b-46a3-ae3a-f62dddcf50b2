export interface ImageItem {
  id: string;
  src: string;
  title: string;
  description: string;
  category: 'kitchen' | 'home' | 'building';
}

export const imageGalleryData: ImageItem[] = [
  // Kitchen Images
  {
    id: 'kitchen-1',
    src: '/kitchen/kitchen1.jpg',
    title: 'Modern Kitchen Design',
    description: 'Sleek contemporary kitchen featuring premium timber cabinetry with clean lines and sophisticated finishes.',
    category: 'kitchen'
  },
  {
    id: 'kitchen-2',
    src: '/kitchen/kitchen2.jpg',
    title: 'Rustic Kitchen Charm',
    description: 'Warm rustic kitchen showcasing natural wood textures and traditional craftsmanship.',
    category: 'kitchen'
  },
  {
    id: 'kitchen-4',
    src: '/kitchen/kitchen4.jpg',
    title: 'Luxury Kitchen Space',
    description: 'Elegant luxury kitchen with high-end timber finishes and premium hardware details.',
    category: 'kitchen'
  },

  // Home Images
  {
    id: 'home-1',
    src: '/home/<USER>',
    title: 'Cozy Living Space',
    description: 'Beautiful living room featuring warm timber accents and comfortable modern furnishings.',
    category: 'home'
  },
  {
    id: 'home-2',
    src: '/home/<USER>',
    title: 'Elegant Interior',
    description: 'Sophisticated interior design with rich wood tones and contemporary styling.',
    category: 'home'
  },
  {
    id: 'home-3',
    src: '/home/<USER>',
    title: 'Natural Wood Features',
    description: 'Stunning home interior highlighting natural wood grain and organic textures.',
    category: 'home'
  },
  {
    id: 'home-4',
    src: '/home/<USER>',
    title: 'Modern Home Design',
    description: 'Clean modern home design with strategic use of timber elements for warmth.',
    category: 'home'
  },
  {
    id: 'home-5',
    src: '/home/<USER>',
    title: 'Timber Accent Wall',
    description: 'Eye-catching timber accent wall creating a focal point in contemporary living space.',
    category: 'home'
  },
  {
    id: 'home-6',
    src: '/home/<USER>',
    title: 'Scandinavian Style',
    description: 'Minimalist Scandinavian-inspired interior with light timber and clean aesthetics.',
    category: 'home'
  },
  {
    id: 'home-7',
    src: '/home/<USER>',
    title: 'Warm Family Room',
    description: 'Inviting family room with rich timber flooring and cozy atmospheric lighting.',
    category: 'home'
  },
  {
    id: 'home-8',
    src: '/home/<USER>',
    title: 'Open Plan Living',
    description: 'Spacious open plan living area with seamless timber transitions and modern flow.',
    category: 'home'
  },
  {
    id: 'home-9',
    src: '/home/<USER>',
    title: 'Luxury Home Interior',
    description: 'Premium home interior showcasing exquisite timber craftsmanship and attention to detail.',
    category: 'home'
  },

  // Building Images
  {
    id: 'building-1',
    src: '/building/building1.jpg',
    title: 'Commercial Timber Frame',
    description: 'Impressive commercial building featuring exposed timber framing and structural elements.',
    category: 'building'
  },
  {
    id: 'building-2',
    src: '/building/building2.jpg',
    title: 'Architectural Excellence',
    description: 'Outstanding architectural design incorporating sustainable timber construction methods.',
    category: 'building'
  },
  {
    id: 'building-3',
    src: '/building/building3.jpg',
    title: 'Structural Innovation',
    description: 'Innovative structural design showcasing the strength and beauty of engineered timber.',
    category: 'building'
  },
  {
    id: 'building-4',
    src: '/building/building4.jpg',
    title: 'Modern Construction',
    description: 'Contemporary building construction utilizing advanced timber engineering techniques.',
    category: 'building'
  },
  {
    id: 'building-home9',
    src: '/building/home9.jpg',
    title: 'Residential Project',
    description: 'Beautiful residential project demonstrating quality timber construction and design.',
    category: 'building'
  }
];

export const getImagesByCategory = (category: 'kitchen' | 'home' | 'building') => {
  return imageGalleryData.filter(item => item.category === category);
};

export const getAllImages = () => {
  return imageGalleryData;
};
