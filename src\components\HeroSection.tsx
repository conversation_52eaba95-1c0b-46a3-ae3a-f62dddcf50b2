'use client';
import { motion } from 'framer-motion';
import { AnimatedButton } from '@/components/AnimatedButton';
import BackgroundEffects from '@/components/BackgroundEffects';
import TextReveal from '@/components/TextReveal';
import FloatingElements from '@/components/FloatingElements';

export default function HeroSection() {
  return (
    <section className="relative h-[700px] flex items-center overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{ backgroundImage: 'url(/images/timber-bg.jpg)' }}
      />

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/60" />

      {/* Animated Background Effects */}
      <BackgroundEffects variant="particles" intensity="medium" className="absolute inset-0">
        <div />
      </BackgroundEffects>
      <FloatingElements count={20} className="absolute inset-0" />

      {/* Content */}
      <div className="container mx-auto text-center text-white relative z-10 px-4">
        <TextReveal
          variant="char-by-char"
          className="text-6xl sm:text-4xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-blue-200 to-emerald-200 bg-clip-text text-transparent"
        >
          Kani Timber Sales Center
        </TextReveal>

        <TextReveal
          variant="fade-up"
          delay={0.8}
          className="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto"
        >
          Premium Timber and Wood Products for Your Dreams
        </TextReveal>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <AnimatedButton
            animation="shimmer"
            size="lg"
            className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-lg px-8 py-4"
            asChild
          >
            <a href="/products">Explore Products</a>
          </AnimatedButton>

          <AnimatedButton
            animation="glow"
            variant="outline"
            size="lg"
            className="border-white/30 text-black hover:bg-white/10 text-lg px-8 py-4"
            asChild
          >
            <a href="/contact">Get Quote</a>
          </AnimatedButton>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="w-1 h-3 bg-white/70 rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}