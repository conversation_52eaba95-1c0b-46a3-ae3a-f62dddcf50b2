'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  duration: number;
  delay: number;
  shape: 'circle' | 'square' | 'triangle';
  color: string;
}

interface FloatingElementsProps {
  count?: number;
  className?: string;
}

export default function FloatingElements({ count = 20, className = '' }: FloatingElementsProps) {
  const [elements, setElements] = useState<FloatingElement[]>([]);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const newElements: FloatingElement[] = [];
    const colors = [
      'rgba(59, 130, 246, 0.1)',   // blue
      'rgba(16, 185, 129, 0.1)',   // emerald
      'rgba(245, 158, 11, 0.1)',   // amber
      'rgba(239, 68, 68, 0.1)',    // red
      'rgba(139, 92, 246, 0.1)',   // violet
      'rgba(236, 72, 153, 0.1)',   // pink
    ];
    
    const shapes: Array<'circle' | 'square' | 'triangle'> = ['circle', 'square', 'triangle'];
    
    for (let i = 0; i < count; i++) {
      newElements.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 60 + 20,
        duration: Math.random() * 20 + 10,
        delay: Math.random() * 5,
        shape: shapes[Math.floor(Math.random() * shapes.length)],
        color: colors[Math.floor(Math.random() * colors.length)],
      });
    }
    setElements(newElements);
  }, [count]);

  const getShapeStyles = (element: FloatingElement) => {
    const baseStyles = {
      width: element.size,
      height: element.size,
      backgroundColor: element.color,
    };

    switch (element.shape) {
      case 'circle':
        return { ...baseStyles, borderRadius: '50%' };
      case 'square':
        return { ...baseStyles, borderRadius: '8px' };
      case 'triangle':
        return {
          width: 0,
          height: 0,
          backgroundColor: 'transparent',
          borderLeft: `${element.size / 2}px solid transparent`,
          borderRight: `${element.size / 2}px solid transparent`,
          borderBottom: `${element.size}px solid ${element.color}`,
        };
      default:
        return baseStyles;
    }
  };

  if (!mounted) return null;

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            ...getShapeStyles(element),
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, Math.sin(element.id) * 20, 0],
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}
