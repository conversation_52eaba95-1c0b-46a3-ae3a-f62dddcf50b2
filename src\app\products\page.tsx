'use client';
import { motion } from 'framer-motion';
import BackgroundEffects from '@/components/BackgroundEffects';
import TextReveal from '@/components/TextReveal';
import FloatingElements from '@/components/FloatingElements';
import { AnimatedButton } from '@/components/AnimatedButton';
import { staggerContainer, staggerItem } from '@/lib/utils';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

const products = [
  {
    name: 'Room Furnitures',
    description: 'Durable and elegant hardwood perfect for furniture and flooring applications.',
    features: ['Sustainable sourcing', 'Premium quality', 'Various species available'],
    images: [
      '/home/<USER>',
      '/home/<USER>',
      '/home/<USER>',
      '/home/<USER>',
      '/home/<USER>',
      '/home/<USER>',
      '/home/<USER>',
      '/home/<USER>'
    ]
  },
  {
    name: 'Kitchen Furnitures',
    description: 'Versatile and strong plywood ideal for construction and marine projects.',
    features: ['Water resistant', 'High strength', 'Multiple thicknesses'],
    images: [
      '/kitchen/kitchen1.jpg',
      '/kitchen/kitchen2.jpg',
      '/kitchen/kitchen3.mp4',
      '/kitchen/kitchen4.jpg'
    ]
  },
  {
    name: 'Buildings',
    description: 'Sturdy engineered beams for heavy-duty structural applications.',
    features: ['Load bearing', 'Engineered strength', 'Custom dimensions'],
    images: [
      '/building/building1.jpg',
      '/building/building2.jpg',
      '/building/building3.jpg',
      '/building/building4.jpg'
    ]
  },
];

type Product = typeof products[number];

export default function Products() {
  const [selectedCard, setSelectedCard] = useState<Product | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const router = useRouter();

  const openModal = (product: Product, image: string) => {
    setSelectedCard(product);
    setSelectedImage(image);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setSelectedCard(null);
    setSelectedImage(null);
  };

  const handleContact = () => {
    setModalOpen(false);
    router.push('/contact');
  };

  return (
    <BackgroundEffects variant="aurora" intensity="low" className="min-h-screen">
      <FloatingElements count={15} />
      <section className="container mx-auto py-16 px-4">
        <div className="text-center mb-16">
          <TextReveal
            variant="char-by-char"
            className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent"
          >
            Our Products
          </TextReveal>
          <TextReveal
            variant="fade-up"
            delay={0.5}
            className="text-xl text-muted-foreground max-w-2xl mx-auto"
          >
            Discover our carefully curated selection of high-quality timber and wood products,
            sourced sustainably and crafted for excellence.
          </TextReveal>
        </div>

        <div className="space-y-16">
          {products.map((product, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <h2 className="text-2xl font-bold mb-8 text-center">
                {product.name}
              </h2>
              <motion.div
                variants={staggerContainer}
                initial="initial"
                animate="animate"
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6"
              >
                {product.images.map((image, imgIndex) => (
                  <motion.div
                    key={imgIndex}
                    variants={staggerItem}
                    className="group cursor-pointer"
                    onClick={() => openModal(product, image)}
                  >
                    <div className="rounded-xl overflow-hidden shadow-md bg-white/90 dark:bg-gray-900/80 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-200 flex flex-col">
                      {image.endsWith('.mp4') ? (
                        <video 
                          src={image}
                          className="w-full h-40 object-cover object-center group-hover:scale-105 transition-transform duration-200"
                          muted
                          autoPlay
                          loop
                        />
                      ) : (
                        <img
                          src={image}
                          alt={product.name}
                          className="w-full h-40 object-cover object-center group-hover:scale-105 transition-transform duration-200"
                        />
                      )}
                      <div className="p-3 flex-1 flex flex-col justify-between">
                        <div>
                          <div className="text-xs font-semibold text-blue-600 mb-1">{product.name}</div>
                          <div className="text-sm text-muted-foreground line-clamp-2">{product.description}</div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Modal for image card details */}
        {modalOpen && selectedCard && selectedImage && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-md w-full p-6 relative"
            >
              <button
                onClick={closeModal}
                className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 dark:hover:text-white text-2xl font-bold"
                aria-label="Close"
              >
                &times;
              </button>
              {selectedImage.endsWith('.mp4') ? (
                <video 
                  src={selectedImage}
                  className="w-full h-56 object-cover object-center rounded-xl mb-4"
                  muted
                  autoPlay
                  loop
                />
              ) : (
                <img
                  src={selectedImage}
                  alt={selectedCard.name}
                  className="w-full h-56 object-cover object-center rounded-xl mb-4"
                />
              )}
              <h2 className="text-xl font-bold mb-2 text-center">
                {selectedCard.name}
              </h2>
              <p className="text-muted-foreground mb-4 text-center">
                {selectedCard.description}
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-sm uppercase tracking-wide text-blue-600 mb-2">
                  Key Features
                </h4>
                <ul className="space-y-2">
                  {selectedCard.features.map((feature: string, featureIndex: number) => (
                    <li key={featureIndex} className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="flex justify-center mt-6">
                <AnimatedButton
                  animation="magnetic"
                  size="lg"
                  className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 px-8"
                  onClick={handleContact}
                >
                  Contact
                </AnimatedButton>
              </div>
            </motion.div>
          </div>
        )}

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-2xl p-8 border border-blue-200/50">
            <h3 className="text-2xl font-bold mb-4">Need Custom Solutions?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              We offer custom cutting, treatment, and finishing services to meet your specific project requirements.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <AnimatedButton
                animation="magnetic"
                size="lg"
                className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700"
              >
                <a href="/contact">Contact Our Experts</a>
              </AnimatedButton>
            </div>
          </div>
        </motion.div>
      </section>
    </BackgroundEffects>
  );
}