'use client';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/Card';
import BackgroundEffects from '@/components/BackgroundEffects';
import TextReveal from '@/components/TextReveal';
// import FloatingElements from '@/components/FloatingElements';
import { AnimatedButton } from '@/components/AnimatedButton';
import { staggerContainer, staggerItem } from '@/lib/utils';

// const timeline = [
//   {
//     year: '1999',
//     title: 'Company Founded',
//     description: 'Started as a small family business with a passion for quality timber and sustainable practices.',
//     icon: '🌱'
//   },
//   {
//     year: '2005',
//     title: 'Expansion',
//     description: 'Expanded operations to include exotic hardwoods and specialized lumber for custom projects.',
//     icon: '🏗️'
//   },
//   {
//     year: '2012',
//     title: 'Sustainability Focus',
//     description: 'Became certified in sustainable forestry practices and eco-friendly timber sourcing.',
//     icon: '🌍'
//   },
//   {
//     year: '2018',
//     title: 'Digital Innovation',
//     description: 'Launched online platform and digital tools for better customer service and inventory management.',
//     icon: '💻'
//   },
//   {
//     year: '2024',
//     title: 'Industry Leader',
//     description: 'Recognized as a leading timber supplier with over 10,000 satisfied customers and 50+ wood species.',
//     icon: '🏆'
//   }
// ];

const values = [
  {
    title: 'Quality First',
    description: 'Every piece of timber is carefully selected and inspected to meet our rigorous quality standards.',
    icon: '⭐'
  },
  {
    title: 'Sustainability',
    description: 'We are committed to responsible forestry practices and environmental conservation.',
    icon: '🌲'
  },
  {
    title: 'Customer Focus',
    description: 'Your satisfaction is our priority. We provide expert guidance and personalized service.',
    icon: '🤝'
  },
  {
    title: 'Innovation',
    description: 'We continuously adopt new technologies and methods to serve you better.',
    icon: '🚀'
  }
];

export default function About() {
  return (
    <div>
      {/* Hero Section */}
      <BackgroundEffects variant="aurora" intensity="medium">
        <section className="container mx-auto py-20 px-4 text-center">
          <TextReveal
            variant="char-by-char"
            className="text-5xl font-bold mb-6 bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent"
          >
            About Kani Timber
          </TextReveal>
          <TextReveal
            variant="fade-up"
            delay={0.5}
            className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8"
          >
            For over two decades, we have been crafting excellence in timber solutions,
            combining traditional craftsmanship with modern innovation to serve our community.
          </TextReveal>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
          </motion.div>
        </section>
      </BackgroundEffects>

      {/* Story Section */}
      <BackgroundEffects variant="dots" intensity="low">
        <section className="container mx-auto py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              {/* <h2 className="text-4xl font-bold mb-6">Our Journey</h2>
              <p className="text-xl text-muted-foreground">
                From humble beginnings to industry leadership, discover how we have grown while staying true to our values.
              </p> */}
            </motion.div>

            <Card
              animation="glow"
              glowColor="rgba(16, 185, 129, 0.3)"
              className="mb-1 p-6"
            >
              <CardContent className="text-center">
                <div className="text-6xl mb-6">🌲</div>
                <h3 className="text-2xl font-bold mb-4">Our Mission</h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  To provide premium quality timber and wood products while maintaining the highest standards
                  of sustainability and customer service. We believe in building lasting relationships with our
                  customers and contributing to a greener future through responsible forestry practices.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </BackgroundEffects>

      {/* Timeline Section */}
      {/* <BackgroundEffects variant="grid" intensity="low">
        <FloatingElements count={8} />
        <section className="container mx-auto py-20 px-4">
          <div className="text-center mb-16">
            <TextReveal
              variant="wave"
              className="text-4xl font-bold mb-6"
            >
              Our Timeline
            </TextReveal>
            <TextReveal
              variant="fade-up"
              delay={0.3}
              className="text-xl text-muted-foreground"
            >
              Key milestones in our journey to excellence
            </TextReveal>
          </div>

          <div className="max-w-4xl mx-auto">
            <motion.div
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              className="space-y-8"
            >
              {timeline.map((item, index) => (
                <motion.div
                  key={index}
                  variants={staggerItem}
                  className="flex flex-col md:flex-row items-center gap-8"
                >
                  <div className="flex-shrink-0">
                    <Card
                      animation="magnetic"
                      className="w-24 h-24 flex items-center justify-center"
                    >
                      <div className="text-3xl">{item.icon}</div>
                    </Card>
                  </div>

                  <div className="flex-1">
                    <Card
                      animation="border-beam"
                      className="p-6 text-center md:text-left"
                    >
                      <div className="text-2xl font-bold text-blue-600 mb-2">{item.year}</div>
                      <h3 className="text-xl font-semibold mb-3">{item.title}</h3>
                      <p className="text-muted-foreground">{item.description}</p>
                    </Card>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>
      </BackgroundEffects> */}

      {/* Values Section */}
      <BackgroundEffects variant="waves" intensity="medium">
        <section className="container mx-auto py-20 px-4 ">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-black">Our Values</h2>
            <p className="text-xl text-black max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {values.map((value, index) => (
              <motion.div key={index} variants={staggerItem}>
                <Card
                  animation="hover-lift"
                  className="h-full text-center p-6 bg-white/10 backdrop-blur-sm border-white/20"
                >
                  <div className="text-4xl mb-4">{value.icon}</div>
                  <h3 className="text-xl font-bold mb-3 text-black">{value.title}</h3>
                  <p className="text-black">{value.description}</p>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </section>
      </BackgroundEffects>

      {/* CTA Section */}
      <section className="container mx-auto py-20 px-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="text-center bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-emerald-950/20 dark:to-blue-950/20 rounded-3xl p-12 border border-emerald-200/50"
        >
          <h2 className="text-4xl font-bold mb-6">Ready to Work With Us?</h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Experience the difference that quality, expertise, and dedication can make for your next project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <AnimatedButton
              animation="shimmer"
              size="lg"
              className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700"
            >
              <a href="/contact">Contact Us Today</a>
            </AnimatedButton>
            <AnimatedButton
              animation="ripple"
              variant="outline"
              size="lg"
              className="border-emerald-300 hover:border-emerald-500"
            >
              <a href="/products">View Our Products</a>
            </AnimatedButton>
          </div>
        </motion.div>
      </section>
    </div>
  );
}