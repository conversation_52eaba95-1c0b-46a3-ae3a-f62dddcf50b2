'use client';

import { motion } from 'framer-motion';
import { Card } from '@/components/Card';
import { getImagesByCategory } from '@/data/imageGallery';

interface GalleryPreviewProps {
  category: 'kitchen' | 'home' | 'building';
  maxItems?: number;
  className?: string;
}

export default function GalleryPreview({ 
  category, 
  maxItems = 6, 
  className = '' 
}: GalleryPreviewProps) {
  const images = getImagesByCategory(category).slice(0, maxItems);

  const staggerContainer = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const staggerItem = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="grid grid-cols-2 md:grid-cols-3 gap-4"
      >
        {images.map((image) => (
          <motion.div
            key={image.id}
            variants={staggerItem}
            className="group cursor-pointer"
          >
            <Card
              animation="hover-lift"
              className="overflow-hidden h-full"
            >
              <div className="relative aspect-square overflow-hidden">
                <img
                  src={image.src}
                  alt={image.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute bottom-0 left-0 right-0 p-3 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                  <h3 className="font-semibold text-sm mb-1">{image.title}</h3>
                  <p className="text-xs opacity-90 line-clamp-2">{image.description}</p>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
