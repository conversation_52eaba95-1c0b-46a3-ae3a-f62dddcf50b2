'use client';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/Card';
import { AnimatedButton } from '@/components/AnimatedButton';
import { Input } from '@/components/Input';
import { Textarea } from '@/components/Textarea';
import BackgroundEffects from '@/components/BackgroundEffects';
import TextReveal from '@/components/TextReveal';
import FloatingElements from '@/components/FloatingElements';
import { staggerContainer, staggerItem } from '@/lib/utils';

const contactInfo = [
  {
    title: 'Visit Our Showroom',
    details: ['V/muthaliyairkulam cheddikulam, no-69, 8th cross'],
    icon: '📍',
    color: 'blue'
  },
  {
    title: 'Call Us',
    details: ['0771767766', 'Mon-Sat: 8AM-6PM', 'Sun: 10AM-4PM'],
    icon: '📞',
    color: 'emerald'
  },
  {
    title: 'Email Us',
    details: ['<EMAIL>'],
    icon: '✉️',
    color: 'purple'
  }
];

const services = [
  'Custom Cutting & Milling',
  'Timber Treatment & Finishing',
  'Delivery & Installation',
  'Project Consultation',
  'Bulk Orders & Wholesale',
  'Emergency Supply Services'
];

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    project: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubmitted(true);
        // Reset form after 3 seconds
        setTimeout(() => {
          setIsSubmitted(false);
          setFormData({ name: '', email: '', phone: '', project: '', message: '' });
        }, 3000);
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div>
      {/* Hero Section */}
      <BackgroundEffects variant="aurora" intensity="low">
        <section className="container mx-auto py-20 px-4 text-center">
          <TextReveal
            variant="char-by-char"
            className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-emerald-600 to-purple-600 bg-clip-text text-transparent"
          >
            Get In Touch
          </TextReveal>
          <TextReveal
            variant="fade-up"
            delay={0.5}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            Ready to start your project? Our timber experts are here to help you find the perfect materials
            and provide professional guidance every step of the way.
          </TextReveal>
        </section>
      </BackgroundEffects>

      {/* Contact Info Section */}
      <BackgroundEffects variant="dots" intensity="low">
        <section className="container mx-auto py-20 px-4">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          >
            {contactInfo.map((info, index) => (
              <motion.div key={index} variants={staggerItem}>
                <Card
                  animation="glow"
                  glowColor={`rgba(${info.color === 'blue' ? '59, 130, 246' : info.color === 'emerald' ? '16, 185, 129' : '139, 92, 246'}, 0.3)`}
                  className="h-full text-center p-6"
                >
                  <div className="text-5xl mb-4">{info.icon}</div>
                  <h3 className="text-xl font-bold mb-4">{info.title}</h3>
                  <div className="space-y-1">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-muted-foreground">
                        {detail}
                      </p>
                    ))}
                  </div>

                  {/* WhatsApp Button for Call Us card */}
                  {info.title === 'Call Us' && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="mt-4"
                    >
                      <button
                        onClick={() => {
                          const phoneNumber = '94771767766'; // Sri Lankan format with country code
                          const message = encodeURIComponent(
                            'Hello Kani Timber Sales Center! I am interested in your timber products and would like to get more information about your services.'
                          );
                          const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;
                          window.open(whatsappUrl, '_blank');
                        }}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        aria-label="Contact us on WhatsApp"
                      >
                        <svg
                          className="w-5 h-5"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <span className="font-medium">WhatsApp</span>
                      </button>
                    </motion.div>
                  )}
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </section>
      </BackgroundEffects>

      {/* Contact Form Section */}
      <BackgroundEffects variant="grid" intensity="low">
        <FloatingElements count={12} />
        <section className="container mx-auto py-20 px-4">
          <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Form */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card
                animation="border-beam"
                className="p-8"
              >
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-3xl font-bold">Send Us a Message</CardTitle>
                  <p className="text-muted-foreground">
                    Fill out the form below and we will get back to you within 24 hours.
                  </p>
                </CardHeader>
                <CardContent>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-6 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
                    >
                      <div className="flex items-center">
                        <span className="text-red-500 mr-2">⚠️</span>
                        <p className="text-red-700 dark:text-red-300">{error}</p>
                      </div>
                    </motion.div>
                  )}

                  {isSubmitted ? (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="text-center py-8"
                    >
                      <div className="text-6xl mb-4">✅</div>
                      <h3 className="text-2xl font-bold text-emerald-600 mb-2">Message Sent!</h3>
                      <p className="text-muted-foreground">
                        Thank you for contacting us. We will be in touch soon!
                      </p>
                    </motion.div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        <Input
                          placeholder="Your Name *"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        <Input
                          type="email"
                          placeholder="Your Email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        <Input
                          placeholder="Phone Number *"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="transition-all duration-300 focus:scale-[1.02]"
                          required
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <Input
                          placeholder="Project Type (e.g., Deck, Flooring, Construction)"
                          value={formData.project}
                          onChange={(e) => handleInputChange('project', e.target.value)}
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <Textarea
                          placeholder="Tell us about your project and requirements..."
                          value={formData.message}
                          onChange={(e) => handleInputChange('message', e.target.value)}
                          rows={4}
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 }}
                      >
                        <AnimatedButton
                          type="submit"
                          animation={isSubmitting ? "none" : "shimmer"}
                          size="lg"
                          className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? 'Sending...' : 'Send Message'}
                        </AnimatedButton>
                      </motion.div>
                    </form>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Services & Info */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-8"
            >
              <Card
                animation="tilt"
                className="p-6"
              >
                <h3 className="text-2xl font-bold mb-6 text-center">Our Services</h3>
                <div className="grid grid-cols-1 gap-3">
                  {services.map((service, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index + 0.5 }}
                      className="flex items-center p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors"
                    >
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3 flex-shrink-0" />
                      <span className="font-medium">{service}</span>
                    </motion.div>
                  ))}
                </div>
              </Card>

              <Card
                animation="magnetic"
                className="p-6 bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-emerald-950/20 dark:to-blue-950/20"
              >
                <h3 className="text-xl font-bold mb-4">Quick Response Guarantee</h3>
                <p className="text-muted-foreground mb-4">
                  We understand that time is crucial for your projects. That is why we guarantee:
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="text-emerald-500 mr-2">⚡</span>
                    Response within 24 hours
                  </li>
                  <li className="flex items-center">
                    <span className="text-emerald-500 mr-2">📋</span>
                    Detailed quotes within 48 hours
                  </li>
                  <li className="flex items-center">
                    <span className="text-emerald-500 mr-2">🚚</span>
                    Fast delivery options available
                  </li>
                </ul>
              </Card>
            </motion.div>
          </div>
        </section>
      </BackgroundEffects>
    </div>
  );
}