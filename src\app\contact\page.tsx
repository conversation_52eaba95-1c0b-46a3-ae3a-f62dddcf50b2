'use client';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/Card';
import { AnimatedButton } from '@/components/AnimatedButton';
import { Input } from '@/components/Input';
import { Textarea } from '@/components/Textarea';
import BackgroundEffects from '@/components/BackgroundEffects';
import TextReveal from '@/components/TextReveal';
import FloatingElements from '@/components/FloatingElements';
import { staggerContainer, staggerItem } from '@/lib/utils';

const contactInfo = [
  {
    title: 'Visit Our Showroom',
    details: ['V/muthaliyairkulam cheddikulam, no-69, 8th cross'],
    icon: '📍',
    color: 'blue'
  },
  {
    title: 'Call Us',
    details: ['0771767766', 'Mon-Fri: 8AM-6PM', 'Sat: 9AM-4PM'],
    icon: '📞',
    color: 'emerald'
  },
  {
    title: 'Email Us',
    details: ['<EMAIL>'],
    icon: '✉️',
    color: 'purple'
  }
];

const services = [
  'Custom Cutting & Milling',
  'Timber Treatment & Finishing',
  'Delivery & Installation',
  'Project Consultation',
  'Bulk Orders & Wholesale',
  'Emergency Supply Services'
];

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    project: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubmitted(true);
        // Reset form after 3 seconds
        setTimeout(() => {
          setIsSubmitted(false);
          setFormData({ name: '', email: '', phone: '', project: '', message: '' });
        }, 3000);
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div>
      {/* Hero Section */}
      <BackgroundEffects variant="aurora" intensity="low">
        <section className="container mx-auto py-20 px-4 text-center">
          <TextReveal
            variant="char-by-char"
            className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-emerald-600 to-purple-600 bg-clip-text text-transparent"
          >
            Get In Touch
          </TextReveal>
          <TextReveal
            variant="fade-up"
            delay={0.5}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            Ready to start your project? Our timber experts are here to help you find the perfect materials
            and provide professional guidance every step of the way.
          </TextReveal>
        </section>
      </BackgroundEffects>

      {/* Contact Info Section */}
      <BackgroundEffects variant="dots" intensity="low">
        <section className="container mx-auto py-20 px-4">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          >
            {contactInfo.map((info, index) => (
              <motion.div key={index} variants={staggerItem}>
                <Card
                  animation="glow"
                  glowColor={`rgba(${info.color === 'blue' ? '59, 130, 246' : info.color === 'emerald' ? '16, 185, 129' : '139, 92, 246'}, 0.3)`}
                  className="h-full text-center p-6"
                >
                  <div className="text-5xl mb-4">{info.icon}</div>
                  <h3 className="text-xl font-bold mb-4">{info.title}</h3>
                  <div className="space-y-1">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-muted-foreground">
                        {detail}
                      </p>
                    ))}
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </section>
      </BackgroundEffects>

      {/* Contact Form Section */}
      <BackgroundEffects variant="grid" intensity="low">
        <FloatingElements count={12} />
        <section className="container mx-auto py-20 px-4">
          <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Form */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card
                animation="border-beam"
                className="p-8"
              >
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-3xl font-bold">Send Us a Message</CardTitle>
                  <p className="text-muted-foreground">
                    Fill out the form below and we will get back to you within 24 hours.
                  </p>
                </CardHeader>
                <CardContent>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-6 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
                    >
                      <div className="flex items-center">
                        <span className="text-red-500 mr-2">⚠️</span>
                        <p className="text-red-700 dark:text-red-300">{error}</p>
                      </div>
                    </motion.div>
                  )}

                  {isSubmitted ? (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="text-center py-8"
                    >
                      <div className="text-6xl mb-4">✅</div>
                      <h3 className="text-2xl font-bold text-emerald-600 mb-2">Message Sent!</h3>
                      <p className="text-muted-foreground">
                        Thank you for contacting us. We will be in touch soon!
                      </p>
                    </motion.div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        <Input
                          placeholder="Your Name *"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        <Input
                          type="email"
                          placeholder="Your Email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        <Input
                          placeholder="Phone Number *"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="transition-all duration-300 focus:scale-[1.02]"
                          required
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <Input
                          placeholder="Project Type (e.g., Deck, Flooring, Construction)"
                          value={formData.project}
                          onChange={(e) => handleInputChange('project', e.target.value)}
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <Textarea
                          placeholder="Tell us about your project and requirements..."
                          value={formData.message}
                          onChange={(e) => handleInputChange('message', e.target.value)}
                          rows={4}
                          className="transition-all duration-300 focus:scale-[1.02]"
                        />
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 }}
                      >
                        <AnimatedButton
                          type="submit"
                          animation={isSubmitting ? "none" : "shimmer"}
                          size="lg"
                          className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? 'Sending...' : 'Send Message'}
                        </AnimatedButton>
                      </motion.div>
                    </form>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Services & Info */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-8"
            >
              <Card
                animation="tilt"
                className="p-6"
              >
                <h3 className="text-2xl font-bold mb-6 text-center">Our Services</h3>
                <div className="grid grid-cols-1 gap-3">
                  {services.map((service, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index + 0.5 }}
                      className="flex items-center p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors"
                    >
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3 flex-shrink-0" />
                      <span className="font-medium">{service}</span>
                    </motion.div>
                  ))}
                </div>
              </Card>

              <Card
                animation="magnetic"
                className="p-6 bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-emerald-950/20 dark:to-blue-950/20"
              >
                <h3 className="text-xl font-bold mb-4">Quick Response Guarantee</h3>
                <p className="text-muted-foreground mb-4">
                  We understand that time is crucial for your projects. That is why we guarantee:
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="text-emerald-500 mr-2">⚡</span>
                    Response within 24 hours
                  </li>
                  <li className="flex items-center">
                    <span className="text-emerald-500 mr-2">📋</span>
                    Detailed quotes within 48 hours
                  </li>
                  <li className="flex items-center">
                    <span className="text-emerald-500 mr-2">🚚</span>
                    Fast delivery options available
                  </li>
                </ul>
              </Card>
            </motion.div>
          </div>
        </section>
      </BackgroundEffects>
    </div>
  );
}