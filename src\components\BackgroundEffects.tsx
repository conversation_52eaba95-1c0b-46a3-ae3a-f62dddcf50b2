'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface BackgroundEffectsProps {
  children: React.ReactNode;
  variant?: 'particles' | 'gradient' | 'grid' | 'waves' | 'aurora' | 'dots';
  intensity?: 'low' | 'medium' | 'high';
  className?: string;
}

export default function BackgroundEffects({ 
  children, 
  variant = 'particles',
  intensity = 'medium',
  className = '' 
}: BackgroundEffectsProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const getParticleCount = () => {
    switch (intensity) {
      case 'low': return 20;
      case 'medium': return 40;
      case 'high': return 80;
      default: return 40;
    }
  };

  const renderParticles = () => {
    const count = getParticleCount();
    return (
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(count)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-white/10"
            style={{
              width: Math.random() * 4 + 1,
              height: Math.random() * 4 + 1,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0.1, 0.5, 0.1],
            }}
            transition={{
              duration: Math.random() * 20 + 10,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        ))}
      </div>
    );
  };

  const renderGradient = () => (
    <motion.div
      className="absolute inset-0"
      style={{
        background: 'linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)',
        backgroundSize: '400% 400%',
      }}
      animate={{
        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
      }}
      transition={{
        duration: 15,
        repeat: Infinity,
        ease: "linear",
      }}
    />
  );

  const renderGrid = () => (
    <div className="absolute inset-0">
      <motion.div
        className="absolute inset-0 opacity-10"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
        }}
        animate={{
          backgroundPosition: ['0px 0px', '50px 50px'],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
      />
    </div>
  );

  const renderWaves = () => (
    <div className="absolute inset-0 overflow-hidden">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute inset-0"
          style={{
            background: `radial-gradient(circle at 50% 50%, rgba(59, 130, 246, ${0.1 - i * 0.02}) 0%, transparent 70%)`,
          }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.1, 0.3],
          }}
          transition={{
            duration: 8 + i * 2,
            repeat: Infinity,
            delay: i * 2,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );

  const renderAurora = () => (
    <div className="absolute inset-0 overflow-hidden">
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute inset-0"
          style={{
            background: `linear-gradient(${45 + i * 30}deg, 
              rgba(16, 185, 129, 0.1) 0%, 
              rgba(59, 130, 246, 0.1) 25%, 
              rgba(139, 92, 246, 0.1) 50%, 
              rgba(236, 72, 153, 0.1) 75%, 
              transparent 100%)`,
            filter: 'blur(40px)',
          }}
          animate={{
            x: ['-100%', '100%'],
            opacity: [0, 0.5, 0],
          }}
          transition={{
            duration: 15 + i * 5,
            repeat: Infinity,
            delay: i * 3,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );

  const renderDots = () => {
    // Dots are generated via CSS pattern instead of individual elements
    return (
      <div className="absolute inset-0">
        <svg className="w-full h-full">
          <defs>
            <pattern
              id="dots"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <motion.circle
                cx="20"
                cy="20"
                r="1"
                fill="rgba(255,255,255,0.1)"
                animate={{
                  r: [1, 2, 1],
                  opacity: [0.1, 0.3, 0.1],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#dots)" />
        </svg>
      </div>
    );
  };

  if (!mounted) return <div className={className}>{children}</div>;

  return (
    <div className={`relative ${className}`}>
      {variant === 'particles' && renderParticles()}
      {variant === 'gradient' && renderGradient()}
      {variant === 'grid' && renderGrid()}
      {variant === 'waves' && renderWaves()}
      {variant === 'aurora' && renderAurora()}
      {variant === 'dots' && renderDots()}
      <div className="relative z-10">{children}</div>
    </div>
  );
}
